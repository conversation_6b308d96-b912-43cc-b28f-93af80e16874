import { motion, AnimatePresence } from "motion/react"
import type React from "react"
import { useEffect, useRef, useState } from "react"
import Divider from "@/components/ui/Divider"
import { useRoundPhase } from "@/hooks"
import { useMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { imageCache } from "@/lib/image-cache"
import { EnhancedErrorBoundary, logger } from "@/middleware"
import { bonusSelectors } from "@config/bonus-config"
import { SelectableCell } from "@config/selector-config"
import BonusSymbol from "./bonus-symbol"

const STYLES = {
  mainContainer:
    "flex flex-col justify-center items-center h-full w-full p-2 text-white gap-1 overflow-hidden relative",
  contentContainer: "flex flex-col w-full justify-end",
  symbolsContainer: "grid auto-rows-min",

  topRow: "grid grid-cols-4 gap-1",
  bottomRow: "grid grid-cols-3 gap-1",

  symbolSize: "bonus-symbol h-auto",

  verticalText:
    "flex flex-col justify-center items-center text-center font-bold tracking-widest leading-tight gap-1",
}

const betButtons: SelectableCell[] = [
  bonusSelectors[6], //gold-bag
  bonusSelectors[1], //ying-yang
  bonusSelectors[2], //treasure-chest
  bonusSelectors[3], //chinese-fan
  bonusSelectors[4], //lantern
  bonusSelectors[5], //chinese-coin
  bonusSelectors[0], //lucky-pot
]

// Dragon image assets for preloading
const DRAGON_ASSETS = [
  "/assets/images/dragon/head.png",
  "/assets/images/dragon/neck.png",
  "/assets/images/dragon/hand-right.png",
  "/assets/images/dragon/hand-left.png",
  "/assets/images/dragon/body-front.png",
  "/assets/images/dragon/body-back.png",
] as const

// Preload dragon images on module load to prevent repeated requests
let dragonImagesPreloaded = false
const preloadDragonImages = () => {
  if (dragonImagesPreloaded) return
  dragonImagesPreloaded = true

  // Use image cache to prevent redundant loading
  imageCache.preloadMultiple(DRAGON_ASSETS).catch((error) => {
    logger.warn("Failed to preload dragon images", {
      context: "BonusBetsPanel",
      data: { error: error.message },
    })
    // Reset flag to allow retry
    dragonImagesPreloaded = false
  })
}

export const BonusSymbolsGrid = ({
  isMobile,
  showDragon = true,
}: {
  isMobile: boolean
  showDragon?: boolean
}) => (
  <div className={cn(STYLES.contentContainer)}>
    {/* Bonus symbols container with auto-sized rows */}
    <div className={cn(STYLES.symbolsContainer)}>
      {/* Top row - 4 symbols */}
      <div
        className={cn(
          STYLES.topRow,
          isMobile && showDragon && "gap-2" // Smaller gap for mobile dragon view
        )}
      >
        {/* 200-1 */}
        <BonusSymbol
          bonus={bonusSelectors[0]} // lucky-pot with 200-1 odds
          className={cn(
            STYLES.symbolSize,
            "justify-self-center" // Position at start of grid cell
          )}
        />
        {/* 20-1 */}
        <BonusSymbol
          bonus={bonusSelectors[1]} // ying-yang with 20-1 odds
          className={cn(
            STYLES.symbolSize,
            "justify-self-center" // Keep middle item centered
          )}
        />
        {/* 20-1 */}
        <BonusSymbol
          bonus={bonusSelectors[5]} // chinese-coin with 20-1 odds
          className={cn(
            STYLES.symbolSize,
            "justify-self-center" // Keep middle item centered
          )}
        />
        {/* 200-1 */}
        <BonusSymbol
          bonus={bonusSelectors[6]} // gold-bag with 200-1 odds
          className={cn(
            STYLES.symbolSize,
            "justify-self-center" // Position at end of grid cell
          )}
        />
      </div>

      {/* Bottom row - 3 symbols (repeating the vertical ones) */}
      <div
        className={cn(
          STYLES.bottomRow,
          isMobile && showDragon && "gap-2" // Smaller gap for mobile dragon view
        )}
      >
        {/* 3-1 */}
        <BonusSymbol
          bonus={bonusSelectors[2]} // treasure-chest with 3-1 odds
          className={cn(
            STYLES.symbolSize,
            isMobile && showDragon ? "justify-self-center" : "justify-self-end" // Adjust positioning for mobile
          )}
        />
        {/* 1-1 */}
        <BonusSymbol
          bonus={bonusSelectors[3]} // chinese-fan with 1-1 odds
          className={cn(
            STYLES.symbolSize,
            "justify-self-center" // Keep middle item centered
          )}
        />
        {/* 3-1 */}
        <BonusSymbol
          bonus={bonusSelectors[4]} // lantern with 3-1 odds
          className={cn(
            STYLES.symbolSize,
            isMobile && showDragon
              ? "justify-self-center"
              : "justify-self-start" // Adjust positioning for mobile
          )}
        />
      </div>
    </div>
  </div>
)

const BonusBetsPanelInner: React.FC = () => {
  const isMobile = useMobile()
  const { Betting, Spinning } = useRoundPhase()
  const containerRef = useRef<HTMLDivElement>(null)
  const [showDragon, setShowDragon] = useState(!Betting)
  const spritesheetRef = useRef<HTMLImageElement>(null)
  const animationRef = useRef<number>(0)
  const scrollPositionRef = useRef(0)

  // Preload dragon images on component mount to prevent repeated requests
  useEffect(() => {
    preloadDragonImages()
  }, [])

  useEffect(() => {
    const animateScroll = () => {
      if (!spritesheetRef.current || !Spinning) return

      // Simple increment - consistent visual continuity
      scrollPositionRef.current += 2 // pixels per frame (adjust as needed)

      // Apply the scroll transform
      spritesheetRef.current.style.transform = `translateY(-${scrollPositionRef.current}px)`

      // Continue animation
      animationRef.current = requestAnimationFrame(animateScroll)
    }

    if (Spinning) {
      // Start animation when spinning
      animationRef.current = requestAnimationFrame(animateScroll)
    } else {
      // Stop animation and reset position when not spinning
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      scrollPositionRef.current = 0
      if (spritesheetRef.current) {
        spritesheetRef.current.style.transform = "translateY(0px)"
      }
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [Spinning])

  useEffect(() => {
    const updateSymbolSize = () => {
      if (!containerRef.current) return

      const containerHeight = containerRef.current.clientHeight

      let symbolSize: number

      if (isMobile) {
        if (showDragon) {
          const availableHeight = containerHeight * 0.3

          const calculatedSize = availableHeight / 2.5

          symbolSize = Math.max(Math.min(calculatedSize, 48), 24)
        } else {
          const availableHeight = containerHeight - 50
          const itemCount = betButtons.length
          const spacing = 8

          const calculatedSize =
            (availableHeight - spacing * (itemCount - 1)) / itemCount

          symbolSize = Math.max(Math.min(calculatedSize, 44), 24)
        }
      } else {
        const bottomSectionHeight = containerHeight * 0.3

        const calculatedSize = bottomSectionHeight / 2.5

        symbolSize = Math.max(Math.min(calculatedSize, 64), 32)
      }

      containerRef.current.style.setProperty("--symbol-size", `${symbolSize}px`)
    }

    updateSymbolSize()

    const resizeObserver = new ResizeObserver(updateSymbolSize)
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    const handleWindowResize = () => {
      requestAnimationFrame(updateSymbolSize)
    }
    window.addEventListener("resize", handleWindowResize)

    return () => {
      resizeObserver.disconnect()
      window.removeEventListener("resize", handleWindowResize)
    }
  }, [isMobile, showDragon, betButtons.length])

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowDragon(!Betting)
    }, 400)

    return () => clearTimeout(timer)
  }, [Betting])

  const DragonLayout = () => (
    <section className='grid grid-cols-1 lg:grid-cols-[0.5fr_1fr_0.5fr] grid-rows-[auto_auto_auto] h-full pt-1 pb-3'>
      {isMobile && (
        <div className='w-full'>
          <p className='text-2xl px-12 text-center uppercase text-[#bf9a5d] font-bold '>
            bonus bet
          </p>
          <Divider className='w-full' />
        </div>
      )}
      {!isMobile && (
        <>
          <div className={cn(STYLES.verticalText, "row-start-1 col-start-1")}>
            {"BONUS".split("").map((char, index) => (
              <span
                key={`bonus-${index}`}
                className='text-3xl leading-none text-stroke text-stroke-2 text-stroke-amber-500'
              >
                {char}
              </span>
            ))}
          </div>
          <Divider className='w-full row-start-2 col-start-1 my-2' />
        </>
      )}
      <div className='lg:row-span-3 row-span-12 relative w-full h-full self-center overflow-hidden'>
        {/* Dragon container with responsive scaling */}
        <div className='relative w-full h-full scale-[0.9] transform-gpu'>
          <img
            className='absolute -top-6 left-[40%] z-[4]  w-[80%]  max-w-full max-h-full object-contain'
            src='/assets/images/dragon/head.png'
            alt='head'
          />
          <img
            className='absolute -top-6 left-0 z-[2] w-[80%] max-w-full object-contain'
            src='/assets/images/dragon/neck.png'
            alt='tail'
          />
          <span className='absolute lg:left-10 left-7 -bottom-14 w-3/4 h-full z-[3]'>
            <img src='/assets/images/dragon/gold-border.webp' alt='border' />
          </span>
          <span className='absolute lg:left-14 left-7 top-12 w-3/4 h-[75%] overflow-hidden z-[3]'>
            <img
              ref={spritesheetRef}
              src='/assets/images/dragon/bonus-sprite-sheet.webp'
              alt='bonus sprite sheet'
            />
          </span>
          <img
            className='absolute top-1/2 left-5 z-[3] w-[24%]  max-w-full max-h-full object-contain'
            src='/assets/images/dragon/hand-right.png'
            alt='right hand'
          />
          <img
            className='absolute top-1/2 right-0 z-[3] w-[20%] max-w-full max-h-full object-contain'
            src='/assets/images/dragon/hand-left.png'
            alt='left hand'
          />
          <img
            className='absolute -bottom-5 left-[0%] z-[4] w-full max-w-full max-h-full object-contain'
            src='/assets/images/dragon/body-front.png'
            alt='body front'
          />
          <img
            className='absolute -bottom-5 left-[1%] z-[1] w-full max-w-full max-h-full object-contain'
            src='/assets/images/dragon/body-back.png'
            alt='body'
          />
        </div>
      </div>
      {!isMobile && (
        <>
          <Divider className='w-full row-start-2 col-start-3' />
          <div
            className={cn(STYLES.verticalText, "row-start-3 col-start-3 pb-5")}
          >
            {"BET".split("").map((char, index) => (
              <span
                key={`bets-${index}`}
                className='text-3xl leading-none text-stroke text-stroke-2 text-stroke-amber-500'
              >
                {char}
              </span>
            ))}
          </div>
        </>
      )}
    </section>
  )

  const MobileBonusSymbols = () => {
    const symbolCount = betButtons.length

    return (
      <>
        <div className='text-center text-[#bf9a5d] font-bold mb-2'>
          BONUS
          <br /> BET
        </div>
        {/* Use grid instead of flex for more precise control over spacing */}
        <div
          className='grid h-full'
          style={{
            gridTemplateRows: `repeat(${symbolCount}, 1fr)`,
            gap: "8px",
          }}
        >
          {betButtons.map((bonus, index) => (
            <div className='flex justify-center items-center' key={index}>
              <BonusSymbol
                bonus={bonus}
                className={cn(STYLES.symbolSize, "justify-self-center")}
              />
            </div>
          ))}
        </div>
      </>
    )
  }

  return (
    <div ref={containerRef} className={STYLES.mainContainer}>
      <AnimatePresence mode='wait'>
        {!isMobile ? (
          <>
            <motion.div
              key='dragon-layout'
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.5,
                type: "spring",
                stiffness: 100,
                damping: 15,
              }}
              className='w-full h-full'
            >
              <DragonLayout />
            </motion.div>
            <motion.div
              key='bonus-symbols-grid'
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{
                duration: 0.5,
                type: "spring",
                stiffness: 100,
                damping: 15,
              }}
              className='w-full h-auto flex items-end justify-center pb-2'
            >
              <BonusSymbolsGrid isMobile={isMobile} showDragon={showDragon} />
            </motion.div>
          </>
        ) : (
          <>
            {showDragon ? (
              <>
                <motion.div
                  key='mobile-dragon'
                  initial={{ opacity: 0, scale: 0.95, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95, y: -20 }}
                  transition={{
                    duration: 0.5,
                    type: "spring",
                    stiffness: 100,
                    damping: 15,
                  }}
                  className='w-full h-full'
                >
                  <DragonLayout />
                </motion.div>
              </>
            ) : (
              <motion.div
                key='mobile-bonus-symbols'
                initial={{ opacity: 0, scale: 0.95, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: 20 }}
                transition={{
                  duration: 0.5,
                  type: "spring",
                  stiffness: 100,
                  damping: 15,
                }}
                className='w-full h-full flex flex-col py-2'
              >
                <MobileBonusSymbols />
              </motion.div>
            )}
          </>
        )}
      </AnimatePresence>
    </div>
  )
}

/**
 * Component for displaying the Bonus Bets panel with error boundary
 */
const BonusBetsPanel: React.FC = () => {
  return (
    <EnhancedErrorBoundary context='BonusBetsPanel'>
      <BonusBetsPanelInner />
    </EnhancedErrorBoundary>
  )
}

export default BonusBetsPanel
