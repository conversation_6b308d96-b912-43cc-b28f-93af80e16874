import React from "react"
import { ErrorBoundary, FallbackProps } from "react-error-boundary"
import { handleError } from "../errorHandling"
import { errorReporting } from "../errorReporting"

interface EnhancedErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<FallbackProps>
  onError?: (error: Error, info: { componentStack: string }) => void
  context?: string
}

const DefaultFallback = ({ error, resetErrorBoundary }: FallbackProps) => (
  <div className='p-4 rounded-lg bg-global-green'>
    <h2 className='text-lg font-bold mb-2'>Something went wrong</h2>
    <p className='mb-4'>{error.message}</p>
    <button
      onClick={resetErrorBoundary}
      className='px-4 py-2 bg-global-green text-white rounded'
    >
      Try again
    </button>
  </div>
)

export const EnhancedErrorBoundary: React.FC<EnhancedErrorBoundaryProps> = ({
  children,
  fallback: Fallback = DefaultFallback,
  onError,
  context = "UI",
}) => {
  const handleComponentError = (
    error: Error,
    info: { componentStack: string }
  ) => {
    // Use the standardized error handler
    handleError(error, {
      context,
      showToast: false, // Don't show a toast for component errors
      logError: true,
    })

    // Report the error to the error reporting service
    errorReporting.reportError(error, context, {
      componentStack: info.componentStack,
    })

    // Call custom error handler if provided
    if (onError) {
      onError(error, info)
    }
  }

  return (
    <ErrorBoundary
      FallbackComponent={Fallback}
      onError={(error, info) =>
        handleComponentError(error, {
          componentStack: info.componentStack || "",
        })
      }
    >
      {children}
    </ErrorBoundary>
  )
}

export default EnhancedErrorBoundary
