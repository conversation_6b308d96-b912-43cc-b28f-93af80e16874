import { cva, type VariantProps } from 'class-variance-authority'
import React from 'react'
import { cn } from '@/lib/utils'

const gridVariants = cva(
  'grid w-full',
  {
    variants: {
      cols: {
        1: 'grid-cols-1',
        2: 'grid-cols-1 md:grid-cols-2',
        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
        6: 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6',
        12: 'grid-cols-4 sm:grid-cols-6 lg:grid-cols-12',
        auto: 'grid-cols-auto',
        'auto-fill': 'grid-auto-flow-dense',
      },
      gap: {
        none: 'gap-0',
        xs: 'gap-1',
        sm: 'gap-2',
        md: 'gap-4',
        lg: 'gap-6',
        xl: 'gap-8',
      },
      align: {
        start: 'items-start',
        center: 'items-center',
        end: 'items-end',
        stretch: 'items-stretch',
      },
      justify: {
        start: 'justify-start',
        center: 'justify-center',
        end: 'justify-end',
        between: 'justify-between',
        around: 'justify-around',
        evenly: 'justify-evenly',
      },
    },
    defaultVariants: {
      cols: 3,
      gap: 'md',
      align: 'stretch',
      justify: 'start',
    },
  }
)

export interface GridProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof gridVariants> {
  children: React.ReactNode
  autoRows?: boolean
  minChildWidth?: string
  templateColumns?: string
  templateRows?: string
  templateAreas?: string
  autoFlow?: 'row' | 'column' | 'dense' | 'row dense' | 'column dense'
}

/**
 * Grid component for creating responsive grid layouts
 * 
 * @example
 * ```tsx
 * <Grid cols={3} gap="md">
 *   <div>Item 1</div>
 *   <div>Item 2</div>
 *   <div>Item 3</div>
 * </Grid>
 * ```
 * 
 * @example
 * ```tsx
 * <Grid templateColumns="repeat(auto-fill, minmax(200px, 1fr))" gap="lg">
 *   {items.map(item => (
 *     <Card key={item.id}>{item.name}</Card>
 *   ))}
 * </Grid>
 * ```
 */
export function Grid({
  className,
  children,
  cols,
  gap,
  align,
  justify,
  autoRows,
  minChildWidth,
  templateColumns,
  templateRows,
  templateAreas,
  autoFlow,
  ...props
}: GridProps) {
  // Build custom grid styles
  const customGridStyles: React.CSSProperties = {}
  
  if (minChildWidth) {
    customGridStyles.gridTemplateColumns = `repeat(auto-fill, minmax(${minChildWidth}, 1fr))`
  }
  
  if (templateColumns) {
    customGridStyles.gridTemplateColumns = templateColumns
  }
  
  if (templateRows) {
    customGridStyles.gridTemplateRows = templateRows
  }
  
  if (templateAreas) {
    customGridStyles.gridTemplateAreas = templateAreas
  }
  
  if (autoFlow) {
    customGridStyles.gridAutoFlow = autoFlow
  }
  
  if (autoRows) {
    customGridStyles.gridAutoRows = '1fr'
  }
  
  return (
    <div 
      className={cn(gridVariants({ cols, gap, align, justify }), className)}
      style={customGridStyles}
      {...props}
    >
      {children}
    </div>
  )
}

export interface GridItemProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  colSpan?: number | { sm?: number; md?: number; lg?: number; xl?: number }
  rowSpan?: number
  colStart?: number
  rowStart?: number
  colEnd?: number
  rowEnd?: number
  area?: string
}

/**
 * GridItem component for positioning items within a Grid
 * 
 * @example
 * ```tsx
 * <Grid cols={3}>
 *   <GridItem colSpan={2}>Wide item</GridItem>
 *   <GridItem>Normal item</GridItem>
 *   <GridItem colSpan={{ sm: 1, md: 2, lg: 3 }}>Responsive item</GridItem>
 * </Grid>
 * ```
 */
export function GridItem({
  className,
  children,
  colSpan,
  rowSpan,
  colStart,
  rowStart,
  colEnd,
  rowEnd,
  area,
  ...props
}: GridItemProps) {
  // Build span classes based on responsive object or single value
  let spanClasses = ''
  
  if (colSpan) {
    if (typeof colSpan === 'object') {
      if (colSpan.sm) spanClasses += ` col-span-${colSpan.sm}`
      if (colSpan.md) spanClasses += ` md:col-span-${colSpan.md}`
      if (colSpan.lg) spanClasses += ` lg:col-span-${colSpan.lg}`
      if (colSpan.xl) spanClasses += ` xl:col-span-${colSpan.xl}`
    } else {
      spanClasses += ` col-span-${colSpan}`
    }
  }
  
  if (rowSpan) {
    spanClasses += ` row-span-${rowSpan}`
  }
  
  // Build custom grid styles
  const customStyles: React.CSSProperties = {}
  
  if (colStart) {
    customStyles.gridColumnStart = colStart
  }
  
  if (rowStart) {
    customStyles.gridRowStart = rowStart
  }
  
  if (colEnd) {
    customStyles.gridColumnEnd = colEnd
  }
  
  if (rowEnd) {
    customStyles.gridRowEnd = rowEnd
  }
  
  if (area) {
    customStyles.gridArea = area
  }
  
  return (
    <div 
      className={cn(spanClasses, className)}
      style={customStyles}
      {...props}
    >
      {children}
    </div>
  )
}

export default Grid
